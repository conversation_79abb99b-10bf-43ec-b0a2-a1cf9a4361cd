"""
Ray配置管理

管理Ray集群配置、资源分配和性能优化设置。
充分利用Python 3.11的性能优化和新特性。
"""

import os
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from ..config.settings import get_settings


class RayClusterConfig(BaseModel):
    """Ray集群配置"""
    
    # 集群连接配置
    address: Optional[str] = Field(default=None, description="Ray集群地址")
    namespace: str = Field(default="data_trans", description="Ray命名空间")
    local_mode: bool = Field(default=True, description="本地模式")
    
    # 运行时环境配置
    runtime_env: Dict[str, Any] = Field(default_factory=dict, description="运行时环境")
    
    # 资源配置
    num_cpus: Optional[int] = Field(default=None, description="CPU核心数")
    num_gpus: Optional[int] = Field(default=0, description="GPU数量")
    memory: Optional[int] = Field(default=None, description="内存大小(MB)")
    object_store_memory: Optional[int] = Field(default=None, description="对象存储内存(MB)")
    
    # 日志配置
    log_to_driver: bool = Field(default=False, description="日志输出到驱动程序")
    logging_level: str = Field(default="INFO", description="日志级别")
    
    # 性能配置
    ignore_reinit_error: bool = Field(default=True, description="忽略重复初始化错误")
    include_dashboard: bool = Field(default=True, description="包含Dashboard")
    dashboard_host: str = Field(default="0.0.0.0", description="Dashboard主机")
    dashboard_port: int = Field(default=8265, description="Dashboard端口")
    
    @validator('runtime_env', pre=True, always=True)
    def setup_runtime_env(cls, v):
        """设置运行时环境"""
        if not v:
            v = {}
        
        # 设置Python 3.11环境
        v.setdefault("python", "3.11")
        
        # 设置工作目录
        v.setdefault("working_dir", os.getcwd())
        
        # 设置环境变量
        env_vars = v.setdefault("env_vars", {})
        env_vars.setdefault("PYTHONPATH", os.getcwd())
        
        return v


class RayTaskResourceConfig(BaseModel):
    """Ray任务资源配置"""
    
    # CPU资源
    num_cpus: float = Field(default=1.0, description="CPU核心数")
    
    # 内存资源
    memory: Optional[int] = Field(default=None, description="内存大小(MB)")
    
    # GPU资源
    num_gpus: float = Field(default=0.0, description="GPU数量")
    
    # 自定义资源
    resources: Dict[str, float] = Field(default_factory=dict, description="自定义资源")
    
    # 任务配置
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_exceptions: List[str] = Field(default_factory=list, description="重试异常类型")
    
    # 调度配置
    scheduling_strategy: str = Field(default="DEFAULT", description="调度策略")
    placement_group: Optional[str] = Field(default=None, description="放置组")


class RayActorResourceConfig(BaseModel):
    """Ray Actor资源配置"""
    
    # CPU资源
    num_cpus: float = Field(default=1.0, description="CPU核心数")
    
    # 内存资源
    memory: Optional[int] = Field(default=None, description="内存大小(MB)")
    
    # GPU资源
    num_gpus: float = Field(default=0.0, description="GPU数量")
    
    # 自定义资源
    resources: Dict[str, float] = Field(default_factory=dict, description="自定义资源")
    
    # Actor配置
    max_restarts: int = Field(default=3, description="最大重启次数")
    max_task_retries: int = Field(default=3, description="任务最大重试次数")
    
    # 生命周期配置
    lifetime: str = Field(default="non_detached", description="生命周期")
    name: Optional[str] = Field(default=None, description="Actor名称")
    namespace: Optional[str] = Field(default=None, description="命名空间")


class RayPerformanceConfig(BaseModel):
    """Ray性能优化配置"""
    
    # 对象存储配置
    object_store_memory: Optional[int] = Field(default=None, description="对象存储内存(MB)")
    plasma_directory: Optional[str] = Field(default=None, description="Plasma存储目录")
    
    # 任务调度配置
    max_pending_lease_requests_per_scheduling_category: int = Field(
        default=10000, description="每个调度类别的最大待处理租约请求"
    )
    
    # 网络配置
    raylet_heartbeat_timeout_milliseconds: int = Field(
        default=30000, description="Raylet心跳超时(毫秒)"
    )
    
    # GCS配置
    gcs_rpc_server_reconnect_timeout_s: int = Field(
        default=60, description="GCS RPC服务器重连超时(秒)"
    )
    
    # 工作进程配置
    worker_register_timeout_seconds: int = Field(
        default=30, description="工作进程注册超时(秒)"
    )
    
    # 内存管理
    memory_monitor_refresh_ms: int = Field(
        default=250, description="内存监控刷新间隔(毫秒)"
    )
    
    # 任务执行配置
    task_retry_delay_ms: int = Field(default=5000, description="任务重试延迟(毫秒)")
    max_direct_call_object_size: int = Field(
        default=100000, description="直接调用对象最大大小(字节)"
    )


class RayConfigManager:
    """Ray配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.settings = get_settings()
        self._cluster_config: Optional[RayClusterConfig] = None
        self._task_resource_config: Optional[RayTaskResourceConfig] = None
        self._actor_resource_config: Optional[RayActorResourceConfig] = None
        self._performance_config: Optional[RayPerformanceConfig] = None
    
    def get_cluster_config(self) -> RayClusterConfig:
        """获取集群配置
        
        Returns:
            集群配置对象
        """
        if not self._cluster_config:
            self._cluster_config = self._create_cluster_config()
        return self._cluster_config
    
    def get_task_resource_config(self) -> RayTaskResourceConfig:
        """获取任务资源配置
        
        Returns:
            任务资源配置对象
        """
        if not self._task_resource_config:
            self._task_resource_config = self._create_task_resource_config()
        return self._task_resource_config
    
    def get_actor_resource_config(self) -> RayActorResourceConfig:
        """获取Actor资源配置
        
        Returns:
            Actor资源配置对象
        """
        if not self._actor_resource_config:
            self._actor_resource_config = self._create_actor_resource_config()
        return self._actor_resource_config
    
    def get_performance_config(self) -> RayPerformanceConfig:
        """获取性能配置
        
        Returns:
            性能配置对象
        """
        if not self._performance_config:
            self._performance_config = self._create_performance_config()
        return self._performance_config
    
    def _create_cluster_config(self) -> RayClusterConfig:
        """创建集群配置"""
        config_dict = {}
        
        # 从环境变量读取配置
        if ray_address := os.getenv("RAY_ADDRESS"):
            config_dict["address"] = ray_address
            config_dict["local_mode"] = False
        
        if ray_namespace := os.getenv("RAY_NAMESPACE"):
            config_dict["namespace"] = ray_namespace
        
        # CPU配置
        if num_cpus := os.getenv("RAY_NUM_CPUS"):
            config_dict["num_cpus"] = int(num_cpus)
        
        # 内存配置
        if memory := os.getenv("RAY_MEMORY"):
            config_dict["memory"] = int(memory)
        
        # GPU配置
        if num_gpus := os.getenv("RAY_NUM_GPUS"):
            config_dict["num_gpus"] = int(num_gpus)
        
        return RayClusterConfig(**config_dict)
    
    def _create_task_resource_config(self) -> RayTaskResourceConfig:
        """创建任务资源配置"""
        config_dict = {}
        
        # 从环境变量读取配置
        if task_cpus := os.getenv("RAY_TASK_CPUS"):
            config_dict["num_cpus"] = float(task_cpus)
        
        if task_memory := os.getenv("RAY_TASK_MEMORY"):
            config_dict["memory"] = int(task_memory)
        
        if task_gpus := os.getenv("RAY_TASK_GPUS"):
            config_dict["num_gpus"] = float(task_gpus)
        
        return RayTaskResourceConfig(**config_dict)
    
    def _create_actor_resource_config(self) -> RayActorResourceConfig:
        """创建Actor资源配置"""
        config_dict = {}
        
        # 从环境变量读取配置
        if actor_cpus := os.getenv("RAY_ACTOR_CPUS"):
            config_dict["num_cpus"] = float(actor_cpus)
        
        if actor_memory := os.getenv("RAY_ACTOR_MEMORY"):
            config_dict["memory"] = int(actor_memory)
        
        if actor_gpus := os.getenv("RAY_ACTOR_GPUS"):
            config_dict["num_gpus"] = float(actor_gpus)
        
        return RayActorResourceConfig(**config_dict)
    
    def _create_performance_config(self) -> RayPerformanceConfig:
        """创建性能配置"""
        config_dict = {}
        
        # 从环境变量读取配置
        if object_store_memory := os.getenv("RAY_OBJECT_STORE_MEMORY"):
            config_dict["object_store_memory"] = int(object_store_memory)
        
        if plasma_directory := os.getenv("RAY_PLASMA_DIRECTORY"):
            config_dict["plasma_directory"] = plasma_directory
        
        return RayPerformanceConfig(**config_dict)
    
    def get_ray_init_config(self) -> Dict[str, Any]:
        """获取Ray初始化配置
        
        Returns:
            Ray初始化配置字典
        """
        cluster_config = self.get_cluster_config()
        performance_config = self.get_performance_config()
        
        init_config = {
            "namespace": cluster_config.namespace,
            "runtime_env": cluster_config.runtime_env,
            "log_to_driver": cluster_config.log_to_driver,
            "ignore_reinit_error": cluster_config.ignore_reinit_error,
            "include_dashboard": cluster_config.include_dashboard,
            "dashboard_host": cluster_config.dashboard_host,
            "dashboard_port": cluster_config.dashboard_port,
        }
        
        # 集群地址配置
        if cluster_config.address:
            init_config["address"] = cluster_config.address
        else:
            init_config["local_mode"] = cluster_config.local_mode
        
        # 资源配置
        if cluster_config.num_cpus is not None:
            init_config["num_cpus"] = cluster_config.num_cpus
        
        if cluster_config.num_gpus is not None:
            init_config["num_gpus"] = cluster_config.num_gpus
        
        if cluster_config.memory is not None:
            init_config["memory"] = cluster_config.memory
        
        if performance_config.object_store_memory is not None:
            init_config["object_store_memory"] = performance_config.object_store_memory
        
        return init_config


# 全局配置管理器实例
_config_manager: Optional[RayConfigManager] = None


def get_ray_config_manager() -> RayConfigManager:
    """获取Ray配置管理器实例
    
    Returns:
        配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = RayConfigManager()
    return _config_manager
