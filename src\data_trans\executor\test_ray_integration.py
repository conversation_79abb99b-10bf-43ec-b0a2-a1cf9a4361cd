"""
Ray集成测试脚本

简单的测试脚本，验证Ray分布式计算集成的基本功能。
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_imports():
    """测试基本导入"""
    logger.info("测试基本导入...")
    
    try:
        from data_trans.executor import (
            RayExecutor,
            RayTaskExecutor,
            RayResourceManager,
            RayClusterManager,
            RayStateManager,
            Python311Validator,
            RayIntegrationTestSuite
        )
        logger.info("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        logger.error(f"✗ 导入失败: {e}")
        return False


async def test_python311_compatibility():
    """测试Python 3.11兼容性"""
    logger.info("测试Python 3.11兼容性...")
    
    try:
        from data_trans.executor.python311_compatibility import run_python311_validation
        
        report = await run_python311_validation()
        
        logger.info(f"Python版本: {report['python_version']}")
        logger.info(f"Python 3.11+: {report['is_python311_plus']}")
        logger.info(f"特性支持得分: {report['feature_support']['score']:.1f}%")
        
        if report['is_python311_plus']:
            logger.info("✓ Python 3.11兼容性测试通过")
            return True
        else:
            logger.warning("⚠ 当前不是Python 3.11+版本")
            return False
            
    except Exception as e:
        logger.error(f"✗ Python 3.11兼容性测试失败: {e}")
        return False


async def test_ray_config():
    """测试Ray配置"""
    logger.info("测试Ray配置...")
    
    try:
        from data_trans.executor.ray_config import get_ray_config_manager
        
        config_manager = get_ray_config_manager()
        
        cluster_config = config_manager.get_cluster_config()
        init_config = config_manager.get_ray_init_config()
        
        logger.info(f"集群配置命名空间: {cluster_config.namespace}")
        logger.info(f"本地模式: {cluster_config.local_mode}")
        logger.info(f"初始化配置键: {list(init_config.keys())}")
        
        logger.info("✓ Ray配置测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ Ray配置测试失败: {e}")
        return False


async def test_ray_fallback():
    """测试Ray回退机制"""
    logger.info("测试Ray回退机制...")
    
    try:
        from data_trans.executor.ray_task_executor import RayTaskExecutor, RayTaskExecutorConfig
        
        config = RayTaskExecutorConfig(
            enable_ray_execution=True,
            ray_fallback_to_local=True,
            timeout=10.0
        )
        
        executor = RayTaskExecutor(config)
        
        # 获取Ray状态
        ray_status = executor.get_ray_status()
        logger.info(f"Ray可用: {ray_status['ray_available']}")
        logger.info(f"Ray初始化: {ray_status['ray_initialized']}")
        logger.info(f"回退启用: {ray_status['fallback_enabled']}")
        
        await executor.cleanup()
        
        logger.info("✓ Ray回退机制测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ Ray回退机制测试失败: {e}")
        return False


async def test_state_manager():
    """测试状态管理器"""
    logger.info("测试状态管理器...")
    
    try:
        from data_trans.executor.ray_state_manager import RayStateManager
        
        state_manager = RayStateManager("test_state")
        
        # 测试基本操作（在Ray不可用时会使用本地实现）
        await state_manager.set("test_key", "test_value")
        value = await state_manager.get("test_key")
        exists = await state_manager.exists("test_key")
        
        logger.info(f"设置和获取值: {value == 'test_value'}")
        logger.info(f"存在性检查: {exists}")
        
        # 清理
        await state_manager.delete("test_key")
        
        logger.info("✓ 状态管理器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 状态管理器测试失败: {e}")
        return False


async def run_basic_tests():
    """运行基础测试"""
    logger.info("开始运行Ray集成基础测试")
    logger.info("=" * 50)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("Python 3.11兼容性", test_python311_compatibility),
        ("Ray配置", test_ray_config),
        ("Ray回退机制", test_ray_fallback),
        ("状态管理器", test_state_manager),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 运行测试: {test_name} ---")
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {e}")
            results[test_name] = False
    
    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    total_tests = len(tests)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总计: {total_tests} 个测试")
    logger.info(f"通过: {passed_tests} 个")
    logger.info(f"失败: {failed_tests} 个")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    return passed_tests == total_tests


async def run_comprehensive_tests():
    """运行综合测试"""
    logger.info("开始运行Ray集成综合测试")
    
    try:
        from data_trans.executor.ray_integration_test import run_ray_integration_tests
        
        report = await run_ray_integration_tests()
        
        # 输出测试结果
        summary = report["summary"]
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过: {summary['passed']}")
        logger.info(f"失败: {summary['failed']}")
        logger.info(f"错误: {summary['errors']}")
        logger.info(f"成功率: {summary['success_rate']*100:.1f}%")
        logger.info(f"执行时间: {summary['duration_seconds']:.2f}秒")
        
        # 输出建议
        if report["recommendations"]:
            logger.info("\n优化建议:")
            for rec in report["recommendations"]:
                logger.info(f"- {rec}")
        
        # 保存详细报告
        report_file = Path("ray_integration_test_report.json")
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"\n详细报告已保存到: {report_file}")
        
        return summary['success_rate'] > 0.7  # 70%以上通过率认为成功
        
    except Exception as e:
        logger.error(f"综合测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("Ray分布式计算集成测试")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"当前工作目录: {Path.cwd()}")
    
    # 运行基础测试
    basic_success = await run_basic_tests()
    
    if basic_success:
        logger.info("\n基础测试通过，继续运行综合测试...")
        comprehensive_success = await run_comprehensive_tests()
        
        if comprehensive_success:
            logger.info("\n🎉 所有测试通过！Ray分布式计算集成成功！")
            return 0
        else:
            logger.error("\n❌ 综合测试失败")
            return 1
    else:
        logger.error("\n❌ 基础测试失败，跳过综合测试")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
