"""
数据采集任务执行器模块

该模块实现完整的数据采集到存储流程，整合爬虫、清洗、存储功能。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的执行器类
from .base_executor import BaseExecutor, ExecutorConfig
from .task_executor import TaskExecutor, TaskExecutorConfig
from .ray_executor import RayExecutor, RayExecutorConfig, RayTaskResult

# 导入Ray相关模块
from .ray_actors import TaskManagerActor, ResourceMonitorActor
from .ray_config import (
    RayClusterConfig,
    RayTaskResourceConfig,
    RayActorResourceConfig,
    RayPerformanceConfig,
    RayConfigManager,
    get_ray_config_manager
)

__all__: list[str] = [
    "BaseExecutor",
    "ExecutorConfig",
    "TaskExecutor",
    "TaskExecutorConfig",
    "RayExecutor",
    "RayExecutorConfig",
    "RayTaskResult",
    "TaskManagerActor",
    "ResourceMonitorActor",
    "RayClusterConfig",
    "RayTaskResourceConfig",
    "RayActorResourceConfig",
    "RayPerformanceConfig",
    "RayConfigManager",
    "get_ray_config_manager",
]
