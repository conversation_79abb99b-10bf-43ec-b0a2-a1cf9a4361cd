"""
数据采集任务执行器模块

该模块实现完整的数据采集到存储流程，整合爬虫、清洗、存储功能。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的执行器类
from .base_executor import BaseExecutor, ExecutorConfig
from .task_executor import TaskExecutor, TaskExecutorConfig
from .ray_executor import RayExecutor, RayExecutorConfig, RayTaskResult
from .ray_task_executor import RayTaskExecutor, RayTaskExecutorConfig

# 导入Ray相关模块
from .ray_actors import TaskManagerActor, ResourceMonitorActor
from .ray_config import (
    RayClusterConfig,
    RayTaskResourceConfig,
    RayActorResourceConfig,
    RayPerformanceConfig,
    RayConfigManager,
    get_ray_config_manager
)
from .ray_resource_manager import (
    RayResourceManager,
    ResourceUsage,
    ResourceAllocation,
    AutoScalingConfig
)
from .ray_cluster_manager import (
    RayClusterManager,
    ClusterStatus,
    NodeStatus,
    ClusterHealth
)
from .ray_state_manager import (
    RayStateManager,
    DistributedStateActor,
    StateEntry,
    StateSnapshot
)

# 导入测试和验证模块
from .python311_compatibility import Python311Validator, Python311Features, PerformanceBenchmark
from .ray_integration_test import RayIntegrationTestSuite

__all__: list[str] = [
    "BaseExecutor",
    "ExecutorConfig",
    "TaskExecutor",
    "TaskExecutorConfig",
    "RayExecutor",
    "RayExecutorConfig",
    "RayTaskResult",
    "RayTaskExecutor",
    "RayTaskExecutorConfig",
    "TaskManagerActor",
    "ResourceMonitorActor",
    "RayClusterConfig",
    "RayTaskResourceConfig",
    "RayActorResourceConfig",
    "RayPerformanceConfig",
    "RayConfigManager",
    "get_ray_config_manager",
    "RayResourceManager",
    "ResourceUsage",
    "ResourceAllocation",
    "AutoScalingConfig",
    "RayClusterManager",
    "ClusterStatus",
    "NodeStatus",
    "ClusterHealth",
    "RayStateManager",
    "DistributedStateActor",
    "StateEntry",
    "StateSnapshot",
    "Python311Validator",
    "Python311Features",
    "PerformanceBenchmark",
    "RayIntegrationTestSuite",
]
