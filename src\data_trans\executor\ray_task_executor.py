"""
Ray任务执行器实现

继承TaskExecutor并扩展Ray分布式执行能力，确保与Python 3.11完全兼容。
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from pydantic import BaseModel, Field

from .task_executor import TaskExecutor, TaskExecutorConfig, TaskConfig
from .base_executor import ExecutionResult, ExecutionStatus
from .ray_config import get_ray_config_manager
from ..config.settings import get_settings

# Ray导入和错误处理
try:
    import ray
    from ray import remote
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    
    def remote(*args, **kwargs):
        def decorator(func):
            func._is_ray_remote = True
            return func
        return decorator

logger = structlog.get_logger(__name__)


class RayTaskExecutorConfig(TaskExecutorConfig):
    """Ray任务执行器配置"""
    
    # Ray特定配置
    enable_ray_execution: bool = Field(default=True, description="启用Ray分布式执行")
    ray_fallback_to_local: bool = Field(default=True, description="Ray失败时回退到本地执行")
    ray_task_timeout: float = Field(default=300.0, description="Ray任务超时时间")
    ray_max_retries: int = Field(default=3, description="Ray任务最大重试次数")
    
    # 资源配置
    ray_cpu_per_task: float = Field(default=1.0, description="每个Ray任务的CPU资源")
    ray_memory_per_task: Optional[int] = Field(default=None, description="每个Ray任务的内存(MB)")
    
    # 批处理配置
    ray_batch_size: int = Field(default=10, description="Ray批处理大小")
    ray_enable_batching: bool = Field(default=True, description="启用Ray批处理")


class RayTaskResult(BaseModel):
    """Ray任务执行结果"""
    
    task_id: str = Field(description="任务ID")
    success: bool = Field(description="是否成功")
    execution_time: float = Field(description="执行时间")
    worker_id: Optional[str] = Field(default=None, description="Ray工作节点ID")
    
    # 执行结果数据
    crawled_count: int = Field(default=0, description="爬取数据条数")
    cleaned_count: int = Field(default=0, description="清洗数据条数")
    stored_count: int = Field(default=0, description="存储数据条数")
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    
    # 日志和指标
    logs: List[str] = Field(default_factory=list, description="执行日志")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="执行指标")


@remote
async def execute_ray_task(
    task_config_dict: Dict[str, Any],
    executor_config_dict: Dict[str, Any],
    settings_dict: Dict[str, Any]
) -> RayTaskResult:
    """Ray远程任务执行函数
    
    Args:
        task_config_dict: 任务配置字典
        executor_config_dict: 执行器配置字典
        settings_dict: 系统设置字典
        
    Returns:
        Ray任务执行结果
    """
    start_time = time.time()
    task_id = task_config_dict.get("task_id", "unknown")
    
    try:
        logger.info("开始执行Ray远程任务", task_id=task_id)
        
        # 在Ray worker中重新创建必要的对象
        from ..config.settings import Settings
        
        # 重建设置对象
        settings = Settings(**settings_dict)
        
        # 创建本地TaskExecutor实例
        executor_config = TaskExecutorConfig(**executor_config_dict)
        executor = TaskExecutor(executor_config)
        
        # 解析任务配置
        task_config = TaskConfig(**task_config_dict)
        
        # 执行任务
        result = await executor.execute_task(task_id, task_config_dict)
        
        execution_time = time.time() - start_time
        
        # 清理资源
        await executor.cleanup()
        
        return RayTaskResult(
            task_id=task_id,
            success=result.status == ExecutionStatus.SUCCESS,
            execution_time=execution_time,
            worker_id=ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local",
            crawled_count=result.crawled_count,
            cleaned_count=result.cleaned_count,
            stored_count=result.stored_count,
            logs=result.logs[-20:],  # 保留最后20条日志
            metrics=result.metrics,
            error_message=result.error_message,
            error_details=result.error_details
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error("Ray远程任务执行失败", task_id=task_id, error=str(e))
        
        return RayTaskResult(
            task_id=task_id,
            success=False,
            execution_time=execution_time,
            worker_id=ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local",
            error_message=str(e),
            error_details={"exception_type": type(e).__name__}
        )


class RayTaskExecutor(TaskExecutor):
    """Ray分布式任务执行器
    
    继承TaskExecutor并添加Ray分布式执行能力。
    在Ray不可用时自动回退到本地执行。
    """
    
    def __init__(self, config: RayTaskExecutorConfig) -> None:
        """初始化Ray任务执行器
        
        Args:
            config: Ray任务执行器配置
        """
        super().__init__(config)
        self.ray_config: RayTaskExecutorConfig = config
        self.settings = get_settings()
        
        # Ray状态
        self._ray_initialized = False
        self._ray_available = RAY_AVAILABLE
        
        # 初始化Ray
        if self.ray_config.enable_ray_execution and RAY_AVAILABLE:
            asyncio.create_task(self._initialize_ray())
    
    async def _initialize_ray(self) -> None:
        """初始化Ray集群"""
        try:
            ray_config_manager = get_ray_config_manager()
            init_config = ray_config_manager.get_ray_init_config()
            
            if not ray.is_initialized():
                ray.init(**init_config)
                logger.info("Ray集群初始化成功")
            
            self._ray_initialized = True
            
        except Exception as e:
            logger.error("Ray集群初始化失败", error=str(e))
            self._ray_initialized = False
            
            if not self.ray_config.ray_fallback_to_local:
                raise RuntimeError(f"Ray初始化失败且未启用本地回退: {e}")
    
    async def execute_task(
        self, 
        task_id: str, 
        task_config: Dict[str, Any]
    ) -> ExecutionResult:
        """执行单个任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            执行结果
        """
        # 检查是否使用Ray执行
        if (self.ray_config.enable_ray_execution and 
            self._ray_available and 
            self._ray_initialized):
            
            try:
                return await self._execute_with_ray(task_id, task_config)
            except Exception as e:
                logger.warning("Ray执行失败，回退到本地执行", error=str(e))
                
                if not self.ray_config.ray_fallback_to_local:
                    raise
        
        # 回退到本地执行
        return await super().execute_task(task_id, task_config)
    
    async def _execute_with_ray(
        self, 
        task_id: str, 
        task_config: Dict[str, Any]
    ) -> ExecutionResult:
        """使用Ray执行任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            执行结果
        """
        logger.info("使用Ray执行任务", task_id=task_id)
        
        # 使用执行上下文管理器
        async with self.execution_context(task_id) as result:
            try:
                # 准备配置字典
                task_config_dict = {**task_config, "task_id": task_id}
                executor_config_dict = self.config.model_dump()
                settings_dict = self.settings.model_dump()
                
                # 提交Ray任务
                if RAY_AVAILABLE:
                    ray_future = execute_ray_task.remote(
                        task_config_dict,
                        executor_config_dict,
                        settings_dict
                    )
                    
                    # 等待任务完成
                    ray_result = await asyncio.wait_for(
                        ray_future,
                        timeout=self.ray_config.ray_task_timeout
                    )
                else:
                    # 本地模拟执行
                    ray_result = await execute_ray_task(
                        task_config_dict,
                        executor_config_dict,
                        settings_dict
                    )
                
                # 处理Ray任务结果
                if ray_result.success:
                    result.status = ExecutionStatus.SUCCESS
                    result.crawled_count = ray_result.crawled_count
                    result.cleaned_count = ray_result.cleaned_count
                    result.stored_count = ray_result.stored_count
                    
                    # 合并日志
                    for log_entry in ray_result.logs:
                        result.add_log(f"[Ray] {log_entry}")
                    
                    # 合并指标
                    result.metrics.update(ray_result.metrics)
                    result.metrics["ray_execution_time"] = ray_result.execution_time
                    result.metrics["ray_worker_id"] = ray_result.worker_id
                    
                    logger.info("Ray任务执行成功", 
                               task_id=task_id,
                               execution_time=ray_result.execution_time)
                else:
                    result.set_error(f"Ray任务执行失败: {ray_result.error_message}")
                    if ray_result.error_details:
                        result.error_details = ray_result.error_details
                    
                    logger.error("Ray任务执行失败", 
                               task_id=task_id,
                               error=ray_result.error_message)
                
            except asyncio.TimeoutError:
                result.status = ExecutionStatus.TIMEOUT
                result.set_error(f"Ray任务执行超时 ({self.ray_config.ray_task_timeout}秒)")
            except Exception as e:
                result.set_error(f"Ray任务执行异常: {e}")
        
        return result
    
    async def execute_batch(
        self, 
        tasks: List[Dict[str, Any]]
    ) -> List[ExecutionResult]:
        """批量执行任务
        
        Args:
            tasks: 任务列表
            
        Returns:
            执行结果列表
        """
        # 检查是否使用Ray批量执行
        if (self.ray_config.enable_ray_execution and 
            self.ray_config.ray_enable_batching and
            self._ray_available and 
            self._ray_initialized):
            
            try:
                return await self._execute_batch_with_ray(tasks)
            except Exception as e:
                logger.warning("Ray批量执行失败，回退到本地执行", error=str(e))
                
                if not self.ray_config.ray_fallback_to_local:
                    raise
        
        # 回退到本地批量执行
        return await super().execute_batch(tasks)
    
    async def _execute_batch_with_ray(
        self, 
        tasks: List[Dict[str, Any]]
    ) -> List[ExecutionResult]:
        """使用Ray批量执行任务
        
        Args:
            tasks: 任务列表
            
        Returns:
            执行结果列表
        """
        logger.info("使用Ray批量执行任务", task_count=len(tasks))
        
        # 准备配置
        executor_config_dict = self.config.model_dump()
        settings_dict = self.settings.model_dump()
        
        # 提交所有Ray任务
        ray_futures = []
        task_ids = []
        
        for task in tasks:
            task_id = task.get("task_id", f"batch_task_{int(time.time())}")
            task_ids.append(task_id)
            
            task_config_dict = {**task, "task_id": task_id}
            
            if RAY_AVAILABLE:
                future = execute_ray_task.remote(
                    task_config_dict,
                    executor_config_dict,
                    settings_dict
                )
            else:
                future = execute_ray_task(
                    task_config_dict,
                    executor_config_dict,
                    settings_dict
                )
            
            ray_futures.append(future)
        
        # 等待所有任务完成
        try:
            if RAY_AVAILABLE:
                ray_results = await asyncio.gather(*ray_futures)
            else:
                ray_results = await asyncio.gather(*ray_futures)
                
        except Exception as e:
            logger.error("Ray批量任务执行异常", error=str(e))
            # 创建失败结果
            ray_results = [
                RayTaskResult(
                    task_id=task_id,
                    success=False,
                    execution_time=0.0,
                    error_message=str(e)
                ) for task_id in task_ids
            ]
        
        # 转换为ExecutionResult
        results = []
        for ray_result in ray_results:
            result = ExecutionResult(
                task_id=ray_result.task_id,
                status=ExecutionStatus.SUCCESS if ray_result.success else ExecutionStatus.FAILED,
                start_time=datetime.utcnow(),
                crawled_count=ray_result.crawled_count,
                cleaned_count=ray_result.cleaned_count,
                stored_count=ray_result.stored_count,
                error_message=ray_result.error_message,
                error_details=ray_result.error_details
            )
            
            # 添加日志
            for log_entry in ray_result.logs:
                result.add_log(f"[Ray] {log_entry}")
            
            # 添加指标
            result.metrics = ray_result.metrics.copy()
            result.metrics["ray_execution_time"] = ray_result.execution_time
            result.metrics["ray_worker_id"] = ray_result.worker_id
            
            result.complete()
            results.append(result)
        
        logger.info("Ray批量任务完成", 
                   total=len(results),
                   success=sum(1 for r in results if r.status == ExecutionStatus.SUCCESS))
        
        return results
    
    def get_ray_status(self) -> Dict[str, Any]:
        """获取Ray状态信息
        
        Returns:
            Ray状态字典
        """
        return {
            "ray_available": self._ray_available,
            "ray_initialized": self._ray_initialized,
            "ray_enabled": self.ray_config.enable_ray_execution,
            "fallback_enabled": self.ray_config.ray_fallback_to_local,
            "cluster_info": self._get_cluster_info() if self._ray_initialized else None
        }
    
    def _get_cluster_info(self) -> Optional[Dict[str, Any]]:
        """获取Ray集群信息"""
        if not self._ray_initialized or not RAY_AVAILABLE:
            return None
        
        try:
            return {
                "cluster_resources": ray.cluster_resources(),
                "available_resources": ray.available_resources(),
                "node_count": len(ray.nodes())
            }
        except Exception as e:
            logger.warning("获取Ray集群信息失败", error=str(e))
            return None
    
    async def cleanup(self) -> None:
        """清理资源"""
        await super().cleanup()
        
        # 清理Ray资源（仅在本地模式下关闭）
        if self._ray_initialized and RAY_AVAILABLE:
            try:
                ray_config_manager = get_ray_config_manager()
                cluster_config = ray_config_manager.get_cluster_config()
                
                if cluster_config.local_mode and not cluster_config.address:
                    ray.shutdown()
                    logger.info("Ray本地集群已关闭")
                    
            except Exception as e:
                logger.warning("Ray清理失败", error=str(e))
        
        logger.info("RayTaskExecutor资源清理完成")
