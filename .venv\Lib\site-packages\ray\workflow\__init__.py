import warnings

from ray.workflow.api import (
    init,
    run,
    run_async,
    resume,
    resume_all,
    resume_async,
    cancel,
    list_all,
    delete,
    get_output,
    get_output_async,
    get_status,
    get_metadata,
    sleep,
    wait_for_event,
    continuation,
    options,
)
from ray.workflow.exceptions import (
    WorkflowError,
    WorkflowExecutionError,
    WorkflowCancellationError,
)
from ray.workflow.common import WorkflowStatus
from ray.workflow.event_listener import EventListener

globals().update(WorkflowStatus.__members__)


__all__ = [
    "init",
    "run",
    "run_async",
    "resume",
    "resume_async",
    "resume_all",
    "cancel",
    "list_all",
    "delete",
    "get_output",
    "get_output_async",
    "get_status",
    "get_metadata",
    "sleep",
    "wait_for_event",
    "options",
    "continuation",
    # events
    "EventListener",
    # exceptions
    "WorkflowError",
    "WorkflowExecutionError",
    "WorkflowCancellationError",
]

warnings.warn(
    "The experimental Ray Workflows library is deprecated and will be removed "
    "in a future version of <PERSON>."
)
