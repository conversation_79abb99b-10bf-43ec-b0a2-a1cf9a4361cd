"""
Ray资源管理器

管理Ray集群资源分配、负载均衡和动态扩缩容。
充分利用Python 3.11的性能优化和新特性。
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import structlog
from pydantic import BaseModel, Field

from .ray_config import get_ray_config_manager

# Ray导入和错误处理
try:
    import ray
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

logger = structlog.get_logger(__name__)


class ResourceUsage(BaseModel):
    """资源使用情况"""
    
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    
    # CPU资源
    total_cpu: float = Field(description="总CPU核心数")
    used_cpu: float = Field(description="已使用CPU核心数")
    available_cpu: float = Field(description="可用CPU核心数")
    cpu_utilization: float = Field(description="CPU使用率")
    
    # 内存资源
    total_memory: int = Field(description="总内存(MB)")
    used_memory: int = Field(description="已使用内存(MB)")
    available_memory: int = Field(description="可用内存(MB)")
    memory_utilization: float = Field(description="内存使用率")
    
    # GPU资源
    total_gpu: float = Field(default=0.0, description="总GPU数量")
    used_gpu: float = Field(default=0.0, description="已使用GPU数量")
    available_gpu: float = Field(default=0.0, description="可用GPU数量")
    gpu_utilization: float = Field(default=0.0, description="GPU使用率")
    
    # 节点信息
    node_count: int = Field(description="节点数量")
    active_nodes: int = Field(description="活跃节点数量")
    
    # 任务信息
    running_tasks: int = Field(default=0, description="运行中任务数")
    pending_tasks: int = Field(default=0, description="等待中任务数")


class ResourceAllocation(BaseModel):
    """资源分配策略"""
    
    # CPU分配
    cpu_per_task: float = Field(default=1.0, description="每个任务CPU核心数")
    cpu_overcommit_ratio: float = Field(default=1.2, description="CPU超分比例")
    
    # 内存分配
    memory_per_task: Optional[int] = Field(default=None, description="每个任务内存(MB)")
    memory_overcommit_ratio: float = Field(default=1.1, description="内存超分比例")
    
    # GPU分配
    gpu_per_task: float = Field(default=0.0, description="每个任务GPU数量")
    
    # 调度策略
    scheduling_strategy: str = Field(default="SPREAD", description="调度策略")
    placement_group_strategy: str = Field(default="PACK", description="放置组策略")
    
    # 负载均衡
    enable_load_balancing: bool = Field(default=True, description="启用负载均衡")
    load_balance_threshold: float = Field(default=0.8, description="负载均衡阈值")


class AutoScalingConfig(BaseModel):
    """自动扩缩容配置"""
    
    # 扩缩容开关
    enable_autoscaling: bool = Field(default=False, description="启用自动扩缩容")
    
    # 扩容配置
    scale_up_threshold: float = Field(default=0.8, description="扩容阈值")
    scale_up_cooldown: int = Field(default=300, description="扩容冷却时间(秒)")
    max_nodes: int = Field(default=10, description="最大节点数")
    
    # 缩容配置
    scale_down_threshold: float = Field(default=0.3, description="缩容阈值")
    scale_down_cooldown: int = Field(default=600, description="缩容冷却时间(秒)")
    min_nodes: int = Field(default=1, description="最小节点数")
    
    # 监控配置
    monitoring_interval: int = Field(default=60, description="监控间隔(秒)")
    decision_window: int = Field(default=300, description="决策窗口(秒)")


class RayResourceManager:
    """Ray资源管理器
    
    负责Ray集群的资源管理、负载均衡和自动扩缩容。
    """
    
    def __init__(
        self,
        allocation_config: Optional[ResourceAllocation] = None,
        autoscaling_config: Optional[AutoScalingConfig] = None
    ):
        """初始化资源管理器
        
        Args:
            allocation_config: 资源分配配置
            autoscaling_config: 自动扩缩容配置
        """
        self.allocation_config = allocation_config or ResourceAllocation()
        self.autoscaling_config = autoscaling_config or AutoScalingConfig()
        
        # 资源使用历史
        self.usage_history: List[ResourceUsage] = []
        self.max_history_size = 1000
        
        # 扩缩容状态
        self.last_scale_up_time: Optional[datetime] = None
        self.last_scale_down_time: Optional[datetime] = None
        
        # 监控状态
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        
        logger.info("Ray资源管理器初始化完成")
    
    def get_current_usage(self) -> Optional[ResourceUsage]:
        """获取当前资源使用情况
        
        Returns:
            资源使用情况，如果Ray不可用则返回None
        """
        if not RAY_AVAILABLE or not ray.is_initialized():
            logger.warning("Ray不可用，无法获取资源使用情况")
            return None
        
        try:
            cluster_resources = ray.cluster_resources()
            available_resources = ray.available_resources()
            nodes = ray.nodes()
            
            # 计算CPU使用情况
            total_cpu = cluster_resources.get("CPU", 0.0)
            available_cpu = available_resources.get("CPU", 0.0)
            used_cpu = total_cpu - available_cpu
            cpu_utilization = used_cpu / total_cpu if total_cpu > 0 else 0.0
            
            # 计算内存使用情况
            total_memory = int(cluster_resources.get("memory", 0))
            available_memory = int(available_resources.get("memory", 0))
            used_memory = total_memory - available_memory
            memory_utilization = used_memory / total_memory if total_memory > 0 else 0.0
            
            # 计算GPU使用情况
            total_gpu = cluster_resources.get("GPU", 0.0)
            available_gpu = available_resources.get("GPU", 0.0)
            used_gpu = total_gpu - available_gpu
            gpu_utilization = used_gpu / total_gpu if total_gpu > 0 else 0.0
            
            # 节点信息
            node_count = len(nodes)
            active_nodes = sum(1 for node in nodes if node.get("Alive", False))
            
            usage = ResourceUsage(
                total_cpu=total_cpu,
                used_cpu=used_cpu,
                available_cpu=available_cpu,
                cpu_utilization=cpu_utilization,
                total_memory=total_memory,
                used_memory=used_memory,
                available_memory=available_memory,
                memory_utilization=memory_utilization,
                total_gpu=total_gpu,
                used_gpu=used_gpu,
                available_gpu=available_gpu,
                gpu_utilization=gpu_utilization,
                node_count=node_count,
                active_nodes=active_nodes
            )
            
            # 添加到历史记录
            self.usage_history.append(usage)
            if len(self.usage_history) > self.max_history_size:
                self.usage_history = self.usage_history[-self.max_history_size:]
            
            return usage
            
        except Exception as e:
            logger.error("获取资源使用情况失败", error=str(e))
            return None
    
    def calculate_optimal_allocation(
        self, 
        task_count: int,
        task_requirements: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """计算最优资源分配
        
        Args:
            task_count: 任务数量
            task_requirements: 任务资源需求
            
        Returns:
            资源分配建议
        """
        current_usage = self.get_current_usage()
        if not current_usage:
            return {"error": "无法获取当前资源使用情况"}
        
        # 默认任务需求
        if not task_requirements:
            task_requirements = {
                "cpu": self.allocation_config.cpu_per_task,
                "memory": self.allocation_config.memory_per_task or 512,
                "gpu": self.allocation_config.gpu_per_task
            }
        
        # 计算总需求
        total_cpu_needed = task_count * task_requirements["cpu"]
        total_memory_needed = task_count * task_requirements["memory"]
        total_gpu_needed = task_count * task_requirements["gpu"]
        
        # 检查资源可用性
        cpu_available = current_usage.available_cpu * self.allocation_config.cpu_overcommit_ratio
        memory_available = current_usage.available_memory * self.allocation_config.memory_overcommit_ratio
        gpu_available = current_usage.available_gpu
        
        # 计算可执行任务数
        max_tasks_by_cpu = int(cpu_available / task_requirements["cpu"]) if task_requirements["cpu"] > 0 else task_count
        max_tasks_by_memory = int(memory_available / task_requirements["memory"]) if task_requirements["memory"] > 0 else task_count
        max_tasks_by_gpu = int(gpu_available / task_requirements["gpu"]) if task_requirements["gpu"] > 0 else task_count
        
        max_concurrent_tasks = min(max_tasks_by_cpu, max_tasks_by_memory, max_tasks_by_gpu)
        
        return {
            "requested_tasks": task_count,
            "max_concurrent_tasks": max_concurrent_tasks,
            "resource_requirements": {
                "total_cpu_needed": total_cpu_needed,
                "total_memory_needed": total_memory_needed,
                "total_gpu_needed": total_gpu_needed
            },
            "resource_availability": {
                "cpu_available": cpu_available,
                "memory_available": memory_available,
                "gpu_available": gpu_available
            },
            "bottleneck": self._identify_bottleneck(
                max_tasks_by_cpu, max_tasks_by_memory, max_tasks_by_gpu
            ),
            "recommendations": self._generate_recommendations(
                current_usage, task_count, max_concurrent_tasks
            )
        }
    
    def _identify_bottleneck(
        self, 
        max_cpu: int, 
        max_memory: int, 
        max_gpu: int
    ) -> str:
        """识别资源瓶颈"""
        min_tasks = min(max_cpu, max_memory, max_gpu)
        
        if min_tasks == max_cpu:
            return "CPU"
        elif min_tasks == max_memory:
            return "Memory"
        elif min_tasks == max_gpu:
            return "GPU"
        else:
            return "Unknown"
    
    def _generate_recommendations(
        self,
        current_usage: ResourceUsage,
        requested_tasks: int,
        max_concurrent_tasks: int
    ) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 资源使用率建议
        if current_usage.cpu_utilization > 0.9:
            recommendations.append("CPU使用率过高，建议增加CPU资源或减少并发任务")
        
        if current_usage.memory_utilization > 0.9:
            recommendations.append("内存使用率过高，建议增加内存或优化内存使用")
        
        if current_usage.gpu_utilization > 0.9:
            recommendations.append("GPU使用率过高，建议增加GPU资源")
        
        # 并发任务建议
        if max_concurrent_tasks < requested_tasks:
            recommendations.append(f"当前资源只能支持{max_concurrent_tasks}个并发任务，建议分批执行或扩容")
        
        # 节点建议
        if current_usage.active_nodes < current_usage.node_count:
            recommendations.append("存在非活跃节点，建议检查节点状态")
        
        return recommendations
    
    async def start_monitoring(self) -> None:
        """开始资源监控"""
        if self.is_monitoring:
            logger.warning("资源监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("开始资源监控")
    
    async def stop_monitoring(self) -> None:
        """停止资源监控"""
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("资源监控已停止")
    
    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                # 获取当前资源使用情况
                current_usage = self.get_current_usage()
                
                if current_usage:
                    # 检查是否需要自动扩缩容
                    if self.autoscaling_config.enable_autoscaling:
                        await self._check_autoscaling(current_usage)
                    
                    # 记录监控日志
                    logger.debug("资源监控",
                               cpu_util=f"{current_usage.cpu_utilization:.2%}",
                               memory_util=f"{current_usage.memory_utilization:.2%}",
                               nodes=current_usage.active_nodes)
                
                await asyncio.sleep(self.autoscaling_config.monitoring_interval)
                
            except Exception as e:
                logger.error("资源监控异常", error=str(e))
                await asyncio.sleep(self.autoscaling_config.monitoring_interval)
    
    async def _check_autoscaling(self, current_usage: ResourceUsage) -> None:
        """检查自动扩缩容"""
        now = datetime.utcnow()
        
        # 检查扩容条件
        if (current_usage.cpu_utilization > self.autoscaling_config.scale_up_threshold or
            current_usage.memory_utilization > self.autoscaling_config.scale_up_threshold):
            
            # 检查冷却时间
            if (not self.last_scale_up_time or 
                (now - self.last_scale_up_time).total_seconds() > self.autoscaling_config.scale_up_cooldown):
                
                if current_usage.node_count < self.autoscaling_config.max_nodes:
                    await self._scale_up()
                    self.last_scale_up_time = now
        
        # 检查缩容条件
        elif (current_usage.cpu_utilization < self.autoscaling_config.scale_down_threshold and
              current_usage.memory_utilization < self.autoscaling_config.scale_down_threshold):
            
            # 检查冷却时间
            if (not self.last_scale_down_time or 
                (now - self.last_scale_down_time).total_seconds() > self.autoscaling_config.scale_down_cooldown):
                
                if current_usage.node_count > self.autoscaling_config.min_nodes:
                    await self._scale_down()
                    self.last_scale_down_time = now
    
    async def _scale_up(self) -> None:
        """扩容操作"""
        logger.info("触发自动扩容")
        # 这里需要根据具体的部署环境实现扩容逻辑
        # 例如：Kubernetes、AWS、GCP等
        pass
    
    async def _scale_down(self) -> None:
        """缩容操作"""
        logger.info("触发自动缩容")
        # 这里需要根据具体的部署环境实现缩容逻辑
        pass
    
    def get_usage_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取资源使用统计
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            统计信息
        """
        if not self.usage_history:
            return {"error": "没有历史数据"}
        
        # 过滤指定时间范围内的数据
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_usage = [
            usage for usage in self.usage_history 
            if usage.timestamp >= cutoff_time
        ]
        
        if not recent_usage:
            return {"error": "指定时间范围内没有数据"}
        
        # 计算统计信息
        cpu_utils = [usage.cpu_utilization for usage in recent_usage]
        memory_utils = [usage.memory_utilization for usage in recent_usage]
        
        return {
            "time_range_hours": hours,
            "data_points": len(recent_usage),
            "cpu_utilization": {
                "avg": sum(cpu_utils) / len(cpu_utils),
                "min": min(cpu_utils),
                "max": max(cpu_utils)
            },
            "memory_utilization": {
                "avg": sum(memory_utils) / len(memory_utils),
                "min": min(memory_utils),
                "max": max(memory_utils)
            },
            "node_count": {
                "current": recent_usage[-1].node_count,
                "avg": sum(usage.node_count for usage in recent_usage) / len(recent_usage)
            }
        }
