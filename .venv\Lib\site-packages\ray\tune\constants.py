# ==================================================
#               Environment Variables
# ==================================================

# NOTE: When adding a new environment variable, please track it in this list.
TUNE_ENV_VARS = {
    "RAY_AIR_LOCAL_CACHE_DIR",
    "TUNE_DISABLE_AUTO_CALLBACK_LOGGERS",
    "TUNE_DISABLE_AUTO_INIT",
    "TUNE_DISABLE_DATED_SUBDIR",
    "TUNE_DISABLE_STRICT_METRIC_CHECKING",
    "TUNE_DISABLE_SIGINT_HANDLER",
    "TUNE_FORCE_TRIAL_CLEANUP_S",
    "TUNE_FUNCTION_THREAD_TIMEOUT_S",
    "TUNE_GLOBAL_CHECKPOINT_S",
    "TUNE_MAX_LEN_IDENTIFIER",
    "TUNE_MAX_PENDING_TRIALS_PG",
    "TUNE_PLACEMENT_GROUP_PREFIX",
    "TUNE_PLACEMENT_GROUP_RECON_INTERVAL",
    "TUNE_PRINT_ALL_TRIAL_ERRORS",
    "TUNE_RESULT_DIR",
    "TUNE_RESULT_BUFFER_LENGTH",
    "TUNE_RESULT_DELIM",
    "TUNE_RESULT_BUFFER_MAX_TIME_S",
    "TUNE_RESULT_BUFFER_MIN_TIME_S",
    "TUNE_WARN_THRESHOLD_S",
    "TUNE_WARN_INSUFFICENT_RESOURCE_THRESHOLD_S",
    "TUNE_WARN_INSUFFICENT_RESOURCE_THRESHOLD_S_AUTOSCALER",
    "TUNE_WARN_EXCESSIVE_EXPERIMENT_CHECKPOINT_SYNC_THRESHOLD_S",
    "TUNE_STATE_REFRESH_PERIOD",
    "TUNE_RESTORE_RETRY_NUM",
}
