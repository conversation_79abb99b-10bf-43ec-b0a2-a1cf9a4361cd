"""
Ray集群管理器

管理Ray集群的生命周期、健康检查和故障恢复。
充分利用Python 3.11的性能优化和新特性。
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

import structlog
from pydantic import BaseModel, Field

from .ray_config import get_ray_config_manager
from .ray_resource_manager import RayResourceManager

# Ray导入和错误处理
try:
    import ray
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

logger = structlog.get_logger(__name__)


class ClusterStatus(str, Enum):
    """集群状态枚举"""
    
    INITIALIZING = "initializing"
    RUNNING = "running"
    DEGRADED = "degraded"
    FAILED = "failed"
    SHUTDOWN = "shutdown"
    UNKNOWN = "unknown"


class NodeStatus(BaseModel):
    """节点状态"""
    
    node_id: str = Field(description="节点ID")
    node_name: str = Field(description="节点名称")
    alive: bool = Field(description="是否存活")
    resources: Dict[str, float] = Field(default_factory=dict, description="节点资源")
    last_heartbeat: Optional[datetime] = Field(default=None, description="最后心跳时间")
    
    # 性能指标
    cpu_usage: float = Field(default=0.0, description="CPU使用率")
    memory_usage: float = Field(default=0.0, description="内存使用率")
    disk_usage: float = Field(default=0.0, description="磁盘使用率")
    
    # 任务信息
    running_tasks: int = Field(default=0, description="运行中任务数")
    completed_tasks: int = Field(default=0, description="已完成任务数")
    failed_tasks: int = Field(default=0, description="失败任务数")


class ClusterHealth(BaseModel):
    """集群健康状态"""
    
    status: ClusterStatus = Field(description="集群状态")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="检查时间")
    
    # 节点信息
    total_nodes: int = Field(description="总节点数")
    healthy_nodes: int = Field(description="健康节点数")
    unhealthy_nodes: int = Field(description="不健康节点数")
    
    # 资源信息
    total_cpu: float = Field(description="总CPU")
    available_cpu: float = Field(description="可用CPU")
    total_memory: int = Field(description="总内存(MB)")
    available_memory: int = Field(description="可用内存(MB)")
    
    # 任务信息
    running_tasks: int = Field(default=0, description="运行中任务数")
    pending_tasks: int = Field(default=0, description="等待中任务数")
    
    # 健康指标
    cpu_utilization: float = Field(description="CPU使用率")
    memory_utilization: float = Field(description="内存使用率")
    
    # 问题列表
    issues: List[str] = Field(default_factory=list, description="发现的问题")
    warnings: List[str] = Field(default_factory=list, description="警告信息")


class RayClusterManager:
    """Ray集群管理器
    
    负责Ray集群的生命周期管理、健康检查和故障恢复。
    """
    
    def __init__(self, resource_manager: Optional[RayResourceManager] = None):
        """初始化集群管理器
        
        Args:
            resource_manager: 资源管理器实例
        """
        self.resource_manager = resource_manager or RayResourceManager()
        self.config_manager = get_ray_config_manager()
        
        # 集群状态
        self.cluster_status = ClusterStatus.UNKNOWN
        self.last_health_check: Optional[datetime] = None
        self.health_history: List[ClusterHealth] = []
        
        # 监控状态
        self.health_check_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        self.health_check_interval = 30  # 秒
        
        # 故障恢复
        self.recovery_attempts = 0
        self.max_recovery_attempts = 3
        self.recovery_cooldown = 300  # 秒
        self.last_recovery_time: Optional[datetime] = None
        
        logger.info("Ray集群管理器初始化完成")
    
    async def initialize_cluster(self) -> bool:
        """初始化Ray集群
        
        Returns:
            是否初始化成功
        """
        if not RAY_AVAILABLE:
            logger.error("Ray不可用，无法初始化集群")
            self.cluster_status = ClusterStatus.FAILED
            return False
        
        try:
            self.cluster_status = ClusterStatus.INITIALIZING
            logger.info("开始初始化Ray集群")
            
            # 检查是否已经初始化
            if ray.is_initialized():
                logger.info("Ray集群已经初始化")
                self.cluster_status = ClusterStatus.RUNNING
                return True
            
            # 获取初始化配置
            init_config = self.config_manager.get_ray_init_config()
            
            # 初始化Ray
            ray.init(**init_config)
            
            # 等待集群就绪
            await self._wait_for_cluster_ready()
            
            self.cluster_status = ClusterStatus.RUNNING
            logger.info("Ray集群初始化成功")
            
            # 开始健康检查
            await self.start_health_monitoring()
            
            return True
            
        except Exception as e:
            logger.error("Ray集群初始化失败", error=str(e))
            self.cluster_status = ClusterStatus.FAILED
            return False
    
    async def _wait_for_cluster_ready(self, timeout: int = 60) -> None:
        """等待集群就绪
        
        Args:
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查集群资源
                cluster_resources = ray.cluster_resources()
                if cluster_resources.get("CPU", 0) > 0:
                    logger.info("集群资源就绪", resources=cluster_resources)
                    return
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.warning("等待集群就绪时出错", error=str(e))
                await asyncio.sleep(1)
        
        raise TimeoutError(f"集群在{timeout}秒内未就绪")
    
    async def shutdown_cluster(self) -> bool:
        """关闭Ray集群
        
        Returns:
            是否关闭成功
        """
        try:
            logger.info("开始关闭Ray集群")
            
            # 停止健康监控
            await self.stop_health_monitoring()
            
            # 关闭Ray
            if RAY_AVAILABLE and ray.is_initialized():
                ray.shutdown()
                logger.info("Ray集群已关闭")
            
            self.cluster_status = ClusterStatus.SHUTDOWN
            return True
            
        except Exception as e:
            logger.error("关闭Ray集群失败", error=str(e))
            return False
    
    async def check_cluster_health(self) -> ClusterHealth:
        """检查集群健康状态
        
        Returns:
            集群健康状态
        """
        if not RAY_AVAILABLE or not ray.is_initialized():
            return ClusterHealth(
                status=ClusterStatus.FAILED,
                total_nodes=0,
                healthy_nodes=0,
                unhealthy_nodes=0,
                total_cpu=0.0,
                available_cpu=0.0,
                total_memory=0,
                available_memory=0,
                cpu_utilization=0.0,
                memory_utilization=0.0,
                issues=["Ray集群未初始化或不可用"]
            )
        
        try:
            # 获取集群信息
            cluster_resources = ray.cluster_resources()
            available_resources = ray.available_resources()
            nodes = ray.nodes()
            
            # 分析节点状态
            total_nodes = len(nodes)
            healthy_nodes = sum(1 for node in nodes if node.get("Alive", False))
            unhealthy_nodes = total_nodes - healthy_nodes
            
            # 计算资源使用情况
            total_cpu = cluster_resources.get("CPU", 0.0)
            available_cpu = available_resources.get("CPU", 0.0)
            total_memory = int(cluster_resources.get("memory", 0))
            available_memory = int(available_resources.get("memory", 0))
            
            cpu_utilization = (total_cpu - available_cpu) / total_cpu if total_cpu > 0 else 0.0
            memory_utilization = (total_memory - available_memory) / total_memory if total_memory > 0 else 0.0
            
            # 确定集群状态
            status = self._determine_cluster_status(
                healthy_nodes, total_nodes, cpu_utilization, memory_utilization
            )
            
            # 收集问题和警告
            issues, warnings = self._analyze_cluster_issues(
                nodes, cpu_utilization, memory_utilization
            )
            
            health = ClusterHealth(
                status=status,
                total_nodes=total_nodes,
                healthy_nodes=healthy_nodes,
                unhealthy_nodes=unhealthy_nodes,
                total_cpu=total_cpu,
                available_cpu=available_cpu,
                total_memory=total_memory,
                available_memory=available_memory,
                cpu_utilization=cpu_utilization,
                memory_utilization=memory_utilization,
                issues=issues,
                warnings=warnings
            )
            
            # 更新状态
            self.cluster_status = status
            self.last_health_check = datetime.utcnow()
            
            # 添加到历史记录
            self.health_history.append(health)
            if len(self.health_history) > 1000:
                self.health_history = self.health_history[-1000:]
            
            return health
            
        except Exception as e:
            logger.error("集群健康检查失败", error=str(e))
            return ClusterHealth(
                status=ClusterStatus.FAILED,
                total_nodes=0,
                healthy_nodes=0,
                unhealthy_nodes=0,
                total_cpu=0.0,
                available_cpu=0.0,
                total_memory=0,
                available_memory=0,
                cpu_utilization=0.0,
                memory_utilization=0.0,
                issues=[f"健康检查失败: {e}"]
            )
    
    def _determine_cluster_status(
        self,
        healthy_nodes: int,
        total_nodes: int,
        cpu_util: float,
        memory_util: float
    ) -> ClusterStatus:
        """确定集群状态"""
        if total_nodes == 0:
            return ClusterStatus.FAILED
        
        # 检查节点健康度
        node_health_ratio = healthy_nodes / total_nodes
        
        if node_health_ratio < 0.5:
            return ClusterStatus.FAILED
        elif node_health_ratio < 0.8:
            return ClusterStatus.DEGRADED
        
        # 检查资源使用情况
        if cpu_util > 0.95 or memory_util > 0.95:
            return ClusterStatus.DEGRADED
        
        return ClusterStatus.RUNNING
    
    def _analyze_cluster_issues(
        self,
        nodes: List[Dict[str, Any]],
        cpu_util: float,
        memory_util: float
    ) -> tuple[List[str], List[str]]:
        """分析集群问题
        
        Returns:
            (问题列表, 警告列表)
        """
        issues = []
        warnings = []
        
        # 检查节点问题
        dead_nodes = [node for node in nodes if not node.get("Alive", False)]
        if dead_nodes:
            issues.append(f"发现{len(dead_nodes)}个死亡节点")
        
        # 检查资源使用情况
        if cpu_util > 0.9:
            if cpu_util > 0.95:
                issues.append(f"CPU使用率过高: {cpu_util:.1%}")
            else:
                warnings.append(f"CPU使用率较高: {cpu_util:.1%}")
        
        if memory_util > 0.9:
            if memory_util > 0.95:
                issues.append(f"内存使用率过高: {memory_util:.1%}")
            else:
                warnings.append(f"内存使用率较高: {memory_util:.1%}")
        
        return issues, warnings
    
    async def start_health_monitoring(self) -> None:
        """开始健康监控"""
        if self.is_monitoring:
            logger.warning("健康监控已在运行")
            return
        
        self.is_monitoring = True
        self.health_check_task = asyncio.create_task(self._health_monitoring_loop())
        logger.info("开始集群健康监控")
    
    async def stop_health_monitoring(self) -> None:
        """停止健康监控"""
        self.is_monitoring = False
        
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("集群健康监控已停止")
    
    async def _health_monitoring_loop(self) -> None:
        """健康监控循环"""
        while self.is_monitoring:
            try:
                health = await self.check_cluster_health()
                
                # 检查是否需要故障恢复
                if health.status in [ClusterStatus.FAILED, ClusterStatus.DEGRADED]:
                    await self._attempt_recovery(health)
                
                # 记录健康状态
                if health.issues:
                    logger.warning("集群健康检查发现问题", 
                                 status=health.status.value,
                                 issues=health.issues)
                elif health.warnings:
                    logger.info("集群健康检查发现警告",
                               status=health.status.value,
                               warnings=health.warnings)
                else:
                    logger.debug("集群健康检查正常",
                                status=health.status.value,
                                cpu_util=f"{health.cpu_utilization:.1%}",
                                memory_util=f"{health.memory_utilization:.1%}")
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error("健康监控异常", error=str(e))
                await asyncio.sleep(self.health_check_interval)
    
    async def _attempt_recovery(self, health: ClusterHealth) -> None:
        """尝试故障恢复
        
        Args:
            health: 当前健康状态
        """
        now = datetime.utcnow()
        
        # 检查恢复冷却时间
        if (self.last_recovery_time and 
            (now - self.last_recovery_time).total_seconds() < self.recovery_cooldown):
            return
        
        # 检查最大恢复尝试次数
        if self.recovery_attempts >= self.max_recovery_attempts:
            logger.error("已达到最大恢复尝试次数，停止自动恢复")
            return
        
        logger.info("开始故障恢复", attempt=self.recovery_attempts + 1)
        
        try:
            # 根据问题类型执行不同的恢复策略
            recovery_success = False
            
            for issue in health.issues:
                if "死亡节点" in issue:
                    recovery_success = await self._recover_dead_nodes()
                elif "CPU使用率过高" in issue:
                    recovery_success = await self._recover_high_cpu_usage()
                elif "内存使用率过高" in issue:
                    recovery_success = await self._recover_high_memory_usage()
            
            if recovery_success:
                logger.info("故障恢复成功")
                self.recovery_attempts = 0
            else:
                self.recovery_attempts += 1
                logger.warning("故障恢复失败", attempts=self.recovery_attempts)
            
            self.last_recovery_time = now
            
        except Exception as e:
            logger.error("故障恢复异常", error=str(e))
            self.recovery_attempts += 1
            self.last_recovery_time = now
    
    async def _recover_dead_nodes(self) -> bool:
        """恢复死亡节点"""
        logger.info("尝试恢复死亡节点")
        # 这里需要根据具体的部署环境实现节点恢复逻辑
        return False
    
    async def _recover_high_cpu_usage(self) -> bool:
        """恢复高CPU使用率"""
        logger.info("尝试恢复高CPU使用率")
        # 可以尝试扩容或调整任务调度
        return False
    
    async def _recover_high_memory_usage(self) -> bool:
        """恢复高内存使用率"""
        logger.info("尝试恢复高内存使用率")
        # 可以尝试清理内存或扩容
        return False
    
    def get_cluster_info(self) -> Dict[str, Any]:
        """获取集群信息
        
        Returns:
            集群信息字典
        """
        return {
            "status": self.cluster_status.value,
            "ray_available": RAY_AVAILABLE,
            "ray_initialized": ray.is_initialized() if RAY_AVAILABLE else False,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "recovery_attempts": self.recovery_attempts,
            "is_monitoring": self.is_monitoring,
            "health_history_count": len(self.health_history)
        }
    
    def get_recent_health_history(self, hours: int = 24) -> List[ClusterHealth]:
        """获取最近的健康历史
        
        Args:
            hours: 时间范围（小时）
            
        Returns:
            健康历史列表
        """
        if not self.health_history:
            return []
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [
            health for health in self.health_history
            if health.timestamp >= cutoff_time
        ]
