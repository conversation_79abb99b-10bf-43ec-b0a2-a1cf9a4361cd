"""
Ray分布式计算集成测试

测试Ray执行器、Actor和配置管理的功能。
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List

import structlog

from .ray_config import get_ray_config_manager
from .ray_executor import RayExecutor, RayExecutorConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = structlog.get_logger(__name__)


async def test_ray_executor_basic():
    """测试Ray执行器基本功能"""
    logger.info("开始测试Ray执行器基本功能")
    
    try:
        # 创建Ray执行器配置
        config = RayExecutorConfig(
            max_concurrent_tasks=2,
            timeout=60.0,
            ray_namespace="test_namespace",
            fallback_to_local=True
        )
        
        # 创建执行器
        executor = RayExecutor(config)
        
        # 等待Ray初始化
        await asyncio.sleep(2)
        
        # 获取集群信息
        cluster_info = executor.get_cluster_info()
        logger.info("集群信息", cluster_info=cluster_info)
        
        # 测试任务配置
        task_config = {
            "task_id": "test_task_001",
            "task_type": "crawl_clean_store",
            "crawler_type": "web",
            "urls": ["https://httpbin.org/json"],
            "crawler_config": {
                "timeout": 30.0,
                "max_retries": 2
            },
            "cleaner_type": "text",
            "cleaner_config": {},
            "cleaning_rules": [
                {
                    "rule_type": "remove_empty",
                    "config": {}
                }
            ],
            "storage_type": "redis",
            "storage_config": {}
        }
        
        # 执行单个任务
        logger.info("执行单个任务")
        result = await executor.execute_task("test_task_001", task_config)
        
        logger.info("任务执行结果", 
                   task_id=result.task_id,
                   status=result.status.value,
                   duration=result.duration,
                   crawled=result.crawled_count,
                   cleaned=result.cleaned_count,
                   stored=result.stored_count)
        
        # 清理资源
        await executor.cleanup()
        
        logger.info("Ray执行器基本功能测试完成")
        return True
        
    except Exception as e:
        logger.error("Ray执行器测试失败", error=str(e))
        return False


async def test_ray_batch_execution():
    """测试Ray批量执行"""
    logger.info("开始测试Ray批量执行")
    
    try:
        config = RayExecutorConfig(
            max_concurrent_tasks=3,
            timeout=120.0,
            fallback_to_local=True
        )
        
        executor = RayExecutor(config)
        await asyncio.sleep(2)
        
        # 创建多个测试任务
        tasks = []
        for i in range(3):
            task = {
                "task_id": f"batch_task_{i:03d}",
                "task_type": "crawl_clean_store",
                "crawler_type": "api",
                "urls": [f"https://httpbin.org/delay/{i+1}"],
                "crawler_config": {
                    "timeout": 60.0
                },
                "cleaner_type": "text",
                "storage_type": "redis"
            }
            tasks.append(task)
        
        # 批量执行
        logger.info("开始批量执行", task_count=len(tasks))
        results = await executor.execute_batch_distributed(tasks)
        
        # 统计结果
        success_count = sum(1 for r in results if r.status.value == "success")
        failed_count = len(results) - success_count
        
        logger.info("批量执行完成",
                   total=len(results),
                   success=success_count,
                   failed=failed_count)
        
        for result in results:
            logger.info("任务结果",
                       task_id=result.task_id,
                       status=result.status.value,
                       duration=result.duration)
        
        await executor.cleanup()
        
        logger.info("Ray批量执行测试完成")
        return True
        
    except Exception as e:
        logger.error("Ray批量执行测试失败", error=str(e))
        return False


async def test_ray_actors():
    """测试Ray Actors"""
    logger.info("开始测试Ray Actors")
    
    try:
        from .ray_actors import TaskManagerActor, ResourceMonitorActor, RAY_AVAILABLE
        
        if not RAY_AVAILABLE:
            logger.warning("Ray不可用，跳过Actor测试")
            return True
        
        # 创建TaskManager Actor
        task_manager = TaskManagerActor.remote("test_task_manager")
        
        # 注册测试任务
        task_id = "actor_test_task_001"
        task_config = {
            "type": "test",
            "data": {"test": True}
        }
        
        success = await task_manager.register_task.remote(task_id, task_config)
        logger.info("任务注册结果", success=success)
        
        # 更新任务状态
        await task_manager.update_task_status.remote(task_id, "running", {"progress": 0.5})
        
        # 获取任务状态
        status = await task_manager.get_task_status.remote(task_id)
        logger.info("任务状态", status=status)
        
        # 完成任务
        await task_manager.complete_task.remote(task_id, True, {"result": "success"})
        
        # 获取统计信息
        stats = await task_manager.get_statistics.remote()
        logger.info("TaskManager统计", stats=stats)
        
        # 创建ResourceMonitor Actor
        resource_monitor = ResourceMonitorActor.remote("test_resource_monitor")
        
        # 获取当前指标
        metrics = await resource_monitor.get_current_metrics.remote()
        logger.info("资源指标", metrics=metrics)
        
        logger.info("Ray Actors测试完成")
        return True
        
    except Exception as e:
        logger.error("Ray Actors测试失败", error=str(e))
        return False


async def test_ray_config():
    """测试Ray配置管理"""
    logger.info("开始测试Ray配置管理")
    
    try:
        config_manager = get_ray_config_manager()
        
        # 获取各种配置
        cluster_config = config_manager.get_cluster_config()
        task_config = config_manager.get_task_resource_config()
        actor_config = config_manager.get_actor_resource_config()
        performance_config = config_manager.get_performance_config()
        
        logger.info("集群配置", config=cluster_config.model_dump())
        logger.info("任务资源配置", config=task_config.model_dump())
        logger.info("Actor资源配置", config=actor_config.model_dump())
        logger.info("性能配置", config=performance_config.model_dump())
        
        # 获取Ray初始化配置
        init_config = config_manager.get_ray_init_config()
        logger.info("Ray初始化配置", config=init_config)
        
        logger.info("Ray配置管理测试完成")
        return True
        
    except Exception as e:
        logger.error("Ray配置管理测试失败", error=str(e))
        return False


async def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行Ray集成测试套件")
    
    tests = [
        ("Ray配置管理", test_ray_config),
        ("Ray执行器基本功能", test_ray_executor_basic),
        ("Ray批量执行", test_ray_batch_execution),
        ("Ray Actors", test_ray_actors),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"运行测试: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "通过" if result else "失败"
            logger.info(f"测试结果: {test_name} - {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"测试异常: {test_name}", error=str(e))
    
    # 汇总结果
    total_tests = len(tests)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    logger.info("测试套件完成",
               total=total_tests,
               passed=passed_tests,
               failed=failed_tests,
               success_rate=f"{passed_tests/total_tests*100:.1f}%")
    
    # 详细结果
    for test_name, result in results.items():
        status = "✓" if result else "✗"
        logger.info(f"{status} {test_name}")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    asyncio.run(run_all_tests())
