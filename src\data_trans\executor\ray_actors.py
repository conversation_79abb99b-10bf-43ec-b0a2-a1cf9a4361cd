"""
Ray Actor实现

定义长期运行的Ray Actor服务，用于分布式状态管理和服务协调。
充分利用Python 3.11的性能优化和新特性。
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from pydantic import BaseModel, Field

# Ray导入和错误处理
try:
    import ray
    from ray import actor
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    
    # 创建Ray actor装饰器的模拟实现
    def actor(*args, **kwargs):
        """Ray actor装饰器的模拟实现"""
        def decorator(cls):
            cls._is_ray_actor = True
            cls._ray_options = kwargs
            return cls
        return decorator

logger = structlog.get_logger(__name__)


class TaskManagerState(BaseModel):
    """任务管理器状态"""
    
    active_tasks: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="活跃任务")
    completed_tasks: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="已完成任务")
    failed_tasks: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="失败任务")
    
    total_processed: int = Field(default=0, description="总处理任务数")
    total_success: int = Field(default=0, description="总成功任务数")
    total_failed: int = Field(default=0, description="总失败任务数")
    
    start_time: datetime = Field(default_factory=datetime.utcnow, description="启动时间")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="最后活动时间")


@actor
class TaskManagerActor:
    """任务管理器Actor
    
    负责管理分布式任务的状态和协调。
    """
    
    def __init__(self, actor_id: str = "task_manager"):
        """初始化任务管理器Actor
        
        Args:
            actor_id: Actor ID
        """
        self.actor_id = actor_id
        self.state = TaskManagerState()
        self.logger = structlog.get_logger(__name__).bind(actor_id=actor_id)
        
        self.logger.info("TaskManagerActor初始化完成")
    
    def register_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """注册新任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            是否注册成功
        """
        try:
            if task_id in self.state.active_tasks:
                self.logger.warning("任务已存在", task_id=task_id)
                return False
            
            self.state.active_tasks[task_id] = {
                "config": task_config,
                "status": "registered",
                "start_time": datetime.utcnow().isoformat(),
                "last_update": datetime.utcnow().isoformat()
            }
            
            self.state.last_activity = datetime.utcnow()
            self.logger.info("任务注册成功", task_id=task_id)
            return True
            
        except Exception as e:
            self.logger.error("任务注册失败", task_id=task_id, error=str(e))
            return False
    
    def update_task_status(
        self, 
        task_id: str, 
        status: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            metadata: 元数据
            
        Returns:
            是否更新成功
        """
        try:
            if task_id not in self.state.active_tasks:
                self.logger.warning("任务不存在", task_id=task_id)
                return False
            
            task_info = self.state.active_tasks[task_id]
            task_info["status"] = status
            task_info["last_update"] = datetime.utcnow().isoformat()
            
            if metadata:
                task_info.setdefault("metadata", {}).update(metadata)
            
            self.state.last_activity = datetime.utcnow()
            self.logger.debug("任务状态更新", task_id=task_id, status=status)
            return True
            
        except Exception as e:
            self.logger.error("任务状态更新失败", task_id=task_id, error=str(e))
            return False
    
    def complete_task(
        self, 
        task_id: str, 
        success: bool, 
        result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """完成任务
        
        Args:
            task_id: 任务ID
            success: 是否成功
            result: 执行结果
            
        Returns:
            是否处理成功
        """
        try:
            if task_id not in self.state.active_tasks:
                self.logger.warning("任务不存在", task_id=task_id)
                return False
            
            task_info = self.state.active_tasks.pop(task_id)
            task_info["end_time"] = datetime.utcnow().isoformat()
            task_info["success"] = success
            
            if result:
                task_info["result"] = result
            
            # 移动到相应的完成列表
            if success:
                self.state.completed_tasks[task_id] = task_info
                self.state.total_success += 1
            else:
                self.state.failed_tasks[task_id] = task_info
                self.state.total_failed += 1
            
            self.state.total_processed += 1
            self.state.last_activity = datetime.utcnow()
            
            self.logger.info("任务完成", task_id=task_id, success=success)
            return True
            
        except Exception as e:
            self.logger.error("任务完成处理失败", task_id=task_id, error=str(e))
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        # 检查活跃任务
        if task_id in self.state.active_tasks:
            return {
                "status": "active",
                **self.state.active_tasks[task_id]
            }
        
        # 检查已完成任务
        if task_id in self.state.completed_tasks:
            return {
                "status": "completed",
                **self.state.completed_tasks[task_id]
            }
        
        # 检查失败任务
        if task_id in self.state.failed_tasks:
            return {
                "status": "failed",
                **self.state.failed_tasks[task_id]
            }
        
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            统计信息字典
        """
        uptime = (datetime.utcnow() - self.state.start_time).total_seconds()
        
        return {
            "actor_id": self.actor_id,
            "uptime_seconds": uptime,
            "start_time": self.state.start_time.isoformat(),
            "last_activity": self.state.last_activity.isoformat(),
            "active_tasks_count": len(self.state.active_tasks),
            "completed_tasks_count": len(self.state.completed_tasks),
            "failed_tasks_count": len(self.state.failed_tasks),
            "total_processed": self.state.total_processed,
            "total_success": self.state.total_success,
            "total_failed": self.state.total_failed,
            "success_rate": (
                self.state.total_success / self.state.total_processed 
                if self.state.total_processed > 0 else 0.0
            )
        }
    
    def list_active_tasks(self) -> List[str]:
        """列出活跃任务ID
        
        Returns:
            活跃任务ID列表
        """
        return list(self.state.active_tasks.keys())
    
    def cleanup_old_tasks(self, max_age_hours: int = 24) -> int:
        """清理旧任务记录
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的任务数量
        """
        try:
            cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
            cleaned_count = 0
            
            # 清理已完成任务
            to_remove = []
            for task_id, task_info in self.state.completed_tasks.items():
                end_time = datetime.fromisoformat(task_info["end_time"]).timestamp()
                if end_time < cutoff_time:
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.state.completed_tasks[task_id]
                cleaned_count += 1
            
            # 清理失败任务
            to_remove = []
            for task_id, task_info in self.state.failed_tasks.items():
                end_time = datetime.fromisoformat(task_info["end_time"]).timestamp()
                if end_time < cutoff_time:
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.state.failed_tasks[task_id]
                cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info("清理旧任务记录", cleaned_count=cleaned_count)
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error("清理旧任务失败", error=str(e))
            return 0


@actor
class ResourceMonitorActor:
    """资源监控Actor
    
    监控Ray集群资源使用情况。
    """
    
    def __init__(self, actor_id: str = "resource_monitor"):
        """初始化资源监控Actor
        
        Args:
            actor_id: Actor ID
        """
        self.actor_id = actor_id
        self.logger = structlog.get_logger(__name__).bind(actor_id=actor_id)
        self.monitoring = False
        self.metrics_history: List[Dict[str, Any]] = []
        
        self.logger.info("ResourceMonitorActor初始化完成")
    
    async def start_monitoring(self, interval_seconds: int = 30) -> None:
        """开始资源监控
        
        Args:
            interval_seconds: 监控间隔（秒）
        """
        if self.monitoring:
            self.logger.warning("资源监控已在运行")
            return
        
        self.monitoring = True
        self.logger.info("开始资源监控", interval=interval_seconds)
        
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保留最近1000条记录
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                await asyncio.sleep(interval_seconds)
                
            except Exception as e:
                self.logger.error("资源监控异常", error=str(e))
                await asyncio.sleep(interval_seconds)
    
    def stop_monitoring(self) -> None:
        """停止资源监控"""
        self.monitoring = False
        self.logger.info("资源监控已停止")
    
    def _collect_metrics(self) -> Dict[str, Any]:
        """收集资源指标
        
        Returns:
            资源指标字典
        """
        try:
            if RAY_AVAILABLE and ray.is_initialized():
                cluster_resources = ray.cluster_resources()
                available_resources = ray.available_resources()
                
                return {
                    "timestamp": datetime.utcnow().isoformat(),
                    "cluster_resources": cluster_resources,
                    "available_resources": available_resources,
                    "node_count": len(ray.nodes()),
                    "cpu_utilization": (
                        1.0 - (available_resources.get("CPU", 0) / cluster_resources.get("CPU", 1))
                    ),
                    "memory_utilization": (
                        1.0 - (available_resources.get("memory", 0) / cluster_resources.get("memory", 1))
                    )
                }
            else:
                return {
                    "timestamp": datetime.utcnow().isoformat(),
                    "status": "ray_not_available",
                    "cpu_utilization": 0.0,
                    "memory_utilization": 0.0
                }
                
        except Exception as e:
            self.logger.error("收集资源指标失败", error=str(e))
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "status": "error",
                "error": str(e)
            }
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前资源指标
        
        Returns:
            当前资源指标
        """
        return self._collect_metrics()
    
    def get_metrics_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取历史指标
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            历史指标列表
        """
        return self.metrics_history[-limit:] if self.metrics_history else []
